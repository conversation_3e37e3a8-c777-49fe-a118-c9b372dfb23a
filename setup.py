#!/usr/bin/env python3
"""
Setup script for EchoGem.

This script allows EchoGem to be installed as a Python package.
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read the README file
readme_file = Path(__file__).parent / "README.md"
long_description = readme_file.read_text(encoding="utf-8") if readme_file.exists() else ""

# Read requirements
requirements_file = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_file.exists():
    requirements = requirements_file.read_text(encoding="utf-8").strip().split("\n")
    requirements = [req.strip() for req in requirements if req.strip() and not req.startswith("#")]

# Read development requirements
dev_requirements_file = Path(__file__).parent / "requirements-dev.txt"
dev_requirements = []
if dev_requirements_file.exists():
    dev_requirements = dev_requirements_file.read_text(encoding="utf-8").strip().split("\n")
    dev_requirements = [req.strip() for req in dev_requirements if req.strip() and not req.startswith("#")]

setup(
    name="echogem",
    version="1.0.0",
    author="EchoGem Team",
    author_email="<EMAIL>",
    description="A powerful CLI chatbot powered by Google Gemini AI",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/echogem/echogem",
    packages=find_packages(exclude=["tests", "tests.*"]),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Communications :: Chat",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Utilities",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": dev_requirements,
        "test": [
            "pytest>=7.0.0",
            "pytest-asyncio>=0.21.0",
            "pytest-cov>=4.0.0",
            "pytest-mock>=3.10.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "echogem=echogem.cli:main",
        ],
    },
    include_package_data=True,
    package_data={
        "echogem": ["*.txt", "*.md"],
    },
    keywords=[
        "ai", "chatbot", "cli", "gemini", "google", "artificial-intelligence",
        "command-line", "conversation", "nlp", "assistant"
    ],
    project_urls={
        "Bug Reports": "https://github.com/echogem/echogem/issues",
        "Source": "https://github.com/echogem/echogem",
        "Documentation": "https://github.com/echogem/echogem#readme",
    },
)
