"""
Command-line interface for EchoGem.

This module contains the CLI implementation and user interaction logic.
"""

import os
import sys
import asyncio
import logging
from typing import Optional
from dotenv import load_dotenv
from colorama import init, Fore, Style

from .agent import GeminiAgent
from .config import APP_NAME, APP_VERSION, ENV_API_KEY
from .utils import ConversationHistory, ConfigManager, validate_api_key, format_timestamp

# Initialize colorama for cross-platform colored output
init()

logger = logging.getLogger(__name__)


class EchoGemCLI:
    """Command-line interface for EchoGem."""

    def __init__(self):
        """Initialize the CLI."""
        self.chatbot: Optional[GeminiAgent] = None
        self.config_manager = ConfigManager()
        self.conversation_history = ConversationHistory(
            max_conversations=self.config_manager.get('max_conversations', 10)
        )
        self.response_mode = self.config_manager.get('default_mode', 1)
    
    def load_environment(self) -> str:
        """Load environment variables and validate API key."""
        load_dotenv()
        api_key = os.getenv(ENV_API_KEY)

        if not api_key:
            print(f"{Fore.RED}Error: {ENV_API_KEY} not found in environment variables.{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}Please create a .env file with your Gemini API key:{Style.RESET_ALL}")
            print(f"{ENV_API_KEY}=your_api_key_here")
            print(f"\n{Fore.CYAN}Get your API key from: https://makersuite.google.com/app/apikey{Style.RESET_ALL}")
            sys.exit(1)

        # Validate API key format
        if not validate_api_key(api_key):
            print(f"{Fore.RED}Warning: API key format appears invalid.{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}Gemini API keys typically start with 'AIza' and are 30-50 characters long.{Style.RESET_ALL}")

        return api_key
    
    def print_welcome(self):
        """Print welcome message and instructions."""
        print(f"\n{Fore.CYAN}🤖 Welcome to {APP_NAME} v{APP_VERSION} - Your Gemini AI Assistant!{Style.RESET_ALL}")
        print(f"{Fore.GREEN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}Available modes:{Style.RESET_ALL}")
        print(f"  {Fore.WHITE}1{Style.RESET_ALL} - Sync Response (default)")
        print(f"  {Fore.WHITE}2{Style.RESET_ALL} - Async Response")
        print(f"  {Fore.WHITE}3{Style.RESET_ALL} - Streaming Response")
        print(f"\n{Fore.YELLOW}Commands:{Style.RESET_ALL}")
        print(f"  {Fore.WHITE}exit{Style.RESET_ALL} - Quit the application")
        print(f"  {Fore.WHITE}clear{Style.RESET_ALL} - Clear conversation history")
        print(f"  {Fore.WHITE}mode{Style.RESET_ALL} - Change response mode")
        print(f"  {Fore.WHITE}info{Style.RESET_ALL} - Show model information")
        print(f"  {Fore.WHITE}history{Style.RESET_ALL} - Show conversation history")
        print(f"  {Fore.WHITE}save{Style.RESET_ALL} - Save current conversation")
        print(f"  {Fore.WHITE}config{Style.RESET_ALL} - Show configuration")
        print(f"  {Fore.WHITE}help{Style.RESET_ALL} - Show this help message")
        print(f"{Fore.GREEN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━{Style.RESET_ALL}\n")
    
    def get_response_mode(self) -> int:
        """Get the response mode from user."""
        while True:
            try:
                print(f"{Fore.CYAN}Choose response mode:{Style.RESET_ALL}")
                print("1. Sync Response")
                print("2. Async Response") 
                print("3. Streaming Response")
                
                choice = input(f"{Fore.YELLOW}Enter mode (1-3, default=1): {Style.RESET_ALL}").strip()
                
                if not choice:
                    return 1
                
                mode = int(choice)
                if mode in [1, 2, 3]:
                    return mode
                else:
                    print(f"{Fore.RED}Please enter 1, 2, or 3{Style.RESET_ALL}")
                    
            except ValueError:
                print(f"{Fore.RED}Please enter a valid number{Style.RESET_ALL}")
    
    async def handle_async_response(self, user_input: str):
        """Handle async response mode."""
        print(f"{Fore.BLUE}🤖 Thinking...{Style.RESET_ALL}")
        response = await self.chatbot.respond_async(user_input)
        print(f"{Fore.GREEN}🤖 {APP_NAME}:{Style.RESET_ALL} {response}")

        # Save to conversation history
        if self.config_manager.get('save_conversations', True):
            self.conversation_history.add_exchange(user_input, response)

    def handle_streaming_response(self, user_input: str):
        """Handle streaming response mode."""
        print(f"{Fore.GREEN}🤖 {APP_NAME}:{Style.RESET_ALL} ", end="", flush=True)

        response_parts = []
        for chunk in self.chatbot.respond_stream(user_input):
            print(chunk, end="", flush=True)
            response_parts.append(chunk)
        print()  # New line after streaming

        # Save to conversation history
        if self.config_manager.get('save_conversations', True):
            full_response = ''.join(response_parts)
            self.conversation_history.add_exchange(user_input, full_response)
    
    def handle_command(self, command: str) -> bool:
        """
        Handle special commands.

        Args:
            command: The command to handle

        Returns:
            True if the command was handled, False if it should exit
        """
        command = command.lower()

        if command == 'exit':
            # Save current conversation before exiting
            if (self.config_manager.get('save_conversations', True) and
                self.conversation_history.current_conversation):
                self.conversation_history.save_conversation()
                print(f"{Fore.GREEN}💾 Conversation saved{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}👋 Goodbye!{Style.RESET_ALL}")
            return False

        elif command == 'clear':
            self.chatbot.clear_history()
            self.conversation_history.clear_current_conversation()
            print(f"{Fore.GREEN}✅ Conversation history cleared{Style.RESET_ALL}")

        elif command == 'mode':
            self.response_mode = self.get_response_mode()
            self.config_manager.set('default_mode', self.response_mode)
            print()

        elif command == 'info':
            info = self.chatbot.get_model_info()
            print(f"{Fore.CYAN}Model Information:{Style.RESET_ALL}")
            print(f"  Model: {info['model_name']}")
            print(f"  Temperature: {info['generation_config']['temperature']}")
            print(f"  Max tokens: {info['generation_config']['max_output_tokens']}")
            print(f"  Chat session active: {info['has_chat_session']}")
            print(f"  Current mode: {self.response_mode} ({'Sync' if self.response_mode == 1 else 'Async' if self.response_mode == 2 else 'Streaming'})")
            print(f"  {self.conversation_history.get_conversation_summary()}")

        elif command == 'history':
            self.show_conversation_history()

        elif command == 'save':
            if self.conversation_history.current_conversation:
                title = input(f"{Fore.YELLOW}Enter conversation title (optional): {Style.RESET_ALL}").strip()
                self.conversation_history.save_conversation(title if title else None)
                print(f"{Fore.GREEN}💾 Conversation saved{Style.RESET_ALL}")
            else:
                print(f"{Fore.YELLOW}No conversation to save{Style.RESET_ALL}")

        elif command == 'config':
            self.show_configuration()

        elif command == 'help':
            self.print_welcome()

        else:
            print(f"{Fore.RED}Unknown command: {command}{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}Type 'help' for available commands{Style.RESET_ALL}")

        return True

    def show_conversation_history(self):
        """Show saved conversation history."""
        conversations = self.conversation_history.load_conversations()

        if not conversations:
            print(f"{Fore.YELLOW}No saved conversations found{Style.RESET_ALL}")
            return

        print(f"{Fore.CYAN}Conversation History:{Style.RESET_ALL}")
        print(f"{Fore.GREEN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━{Style.RESET_ALL}")

        for i, conv in enumerate(conversations[-5:], 1):  # Show last 5 conversations
            timestamp = format_timestamp(conv['timestamp'])
            title = conv['title'][:60] + "..." if len(conv['title']) > 60 else conv['title']
            exchange_count = len(conv['exchanges'])

            print(f"{Fore.WHITE}{i}.{Style.RESET_ALL} {title}")
            print(f"   {Fore.BLUE}📅 {timestamp} | 💬 {exchange_count} exchanges{Style.RESET_ALL}")
            print()

    def show_configuration(self):
        """Show current configuration."""
        print(f"{Fore.CYAN}Current Configuration:{Style.RESET_ALL}")
        print(f"{Fore.GREEN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━{Style.RESET_ALL}")

        config = self.config_manager.config
        for key, value in config.items():
            print(f"  {Fore.WHITE}{key}:{Style.RESET_ALL} {value}")

        print(f"\n{Fore.YELLOW}Note: Configuration is automatically saved when changed{Style.RESET_ALL}")

    async def run(self):
        """Main application loop."""
        try:
            # Load environment and initialize
            api_key = self.load_environment()
            self.print_welcome()
            
            # Initialize chatbot
            print(f"{Fore.BLUE}Initializing Gemini AI...{Style.RESET_ALL}")
            self.chatbot = GeminiAgent(api_key)
            print(f"{Fore.GREEN}✅ Ready to chat!{Style.RESET_ALL}\n")
            
            # Get initial response mode
            self.response_mode = self.get_response_mode()
            print()
            
            # Main chat loop
            while True:
                try:
                    user_input = input(f"{Fore.CYAN}👤 You: {Style.RESET_ALL}").strip()
                    
                    if not user_input:
                        continue
                    
                    # Handle commands
                    if user_input.startswith('/') or user_input.lower() in ['exit', 'clear', 'mode', 'info', 'help']:
                        command = user_input.lstrip('/')
                        if not self.handle_command(command):
                            break
                        continue
                    
                    # Generate response based on mode
                    if self.response_mode == 1:
                        # Sync response
                        response = self.chatbot.respond(user_input)
                        print(f"{Fore.GREEN}🤖 {APP_NAME}:{Style.RESET_ALL} {response}")

                        # Save to conversation history
                        if self.config_manager.get('save_conversations', True):
                            self.conversation_history.add_exchange(user_input, response)

                    elif self.response_mode == 2:
                        # Async response
                        await self.handle_async_response(user_input)

                    elif self.response_mode == 3:
                        # Streaming response
                        self.handle_streaming_response(user_input)
                    
                    print()  # Add spacing between exchanges
                    
                except KeyboardInterrupt:
                    print(f"\n{Fore.YELLOW}👋 Goodbye!{Style.RESET_ALL}")
                    break
                except Exception as e:
                    logger.error(f"Error in main loop: {e}")
                    print(f"{Fore.RED}An error occurred: {e}{Style.RESET_ALL}")
                    
        except Exception as e:
            logger.error(f"Fatal error: {e}")
            print(f"{Fore.RED}Fatal error: {e}{Style.RESET_ALL}")
            sys.exit(1)


def main():
    """Entry point for the CLI application."""
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # Run the CLI
    cli = EchoGemCLI()
    asyncio.run(cli.run())


if __name__ == '__main__':
    main()
