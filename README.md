# EchoGem

A simple, powerful CLI chatbot powered by Google Gemini (Generative AI) using the official `google-generativeai` Python SDK.

---

## Features
- Synchronous, asynchronous, and simulated streaming chat modes
- Easy-to-use command-line interface
- Secure API key management via `.env`
- Clean, minimal dependencies

---

## Setup

### 1. Clone the repository
```bash
git clone <your-repo-url>
cd EchoGem
```

### 2. Install dependencies
```bash
pip install -r requirements.txt
```

### 3. Configure your Gemini API key
Create a `.env` file in the project root:
```
GEMINI_API_KEY=your_google_gemini_api_key_here
```

---

## Usage
Run the CLI:
```bash
python main.py
```

- Enter your prompt when asked.
- Choose a mode:
  - `1` for Sync Response
  - `2` for Async Response
  - `3` for Streaming Response
- Type `exit` to quit.

---

## Environment Variables
- `GEMINI_API_KEY`: Your Google Gemini API key (required)

---

## Dependencies
- [google-generativeai](https://pypi.org/project/google-generativeai/)
- [python-dotenv](https://pypi.org/project/python-dotenv/)

---

## Credits
Inspired by [github:asadullah48](https://github.com/asadullah48)

---
MIT License
