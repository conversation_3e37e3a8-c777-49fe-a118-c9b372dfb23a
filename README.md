# 🤖 EchoGem

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

A powerful, feature-rich CLI chatbot powered by Google Gemini AI. EchoGem provides an intuitive command-line interface for interacting with Google's advanced Gemini models, featuring multiple response modes, conversation history, and extensive customization options.

## ✨ Features

### Core Functionality
- 🚀 **Multiple Response Modes**: Synchronous, asynchronous, and streaming responses
- 💬 **Conversation History**: Automatic saving and retrieval of chat sessions
- ⚙️ **Configurable Settings**: Persistent configuration management
- 🎨 **Rich CLI Interface**: Colorized output with intuitive commands
- 🔒 **Secure API Management**: Environment-based API key handling

### Advanced Features
- 📊 **Model Information**: Real-time model status and configuration display
- 🔄 **Session Management**: Clear history, save conversations, and manage sessions
- 🛠️ **Development Tools**: Comprehensive testing, linting, and formatting setup
- 📦 **Package Structure**: Professional Python package with proper organization
- 🧪 **Testing Suite**: Extensive unit tests with coverage reporting

## 🚀 Quick Start

### Installation

#### Option 1: Install from Source (Recommended for Development)
```bash
# Clone the repository
git clone https://github.com/asadullah48/EchoGem.git
cd EchoGem

# Install in development mode
pip install -e ".[dev,test]"
```

#### Option 2: Install from PyPI (Coming Soon)
```bash
pip install echogem
```

### Configuration

1. **Get your Gemini API key** from [Google AI Studio](https://makersuite.google.com/app/apikey)

2. **Create a `.env` file** in the project root:
```bash
# Copy the example file
cp .env.example .env

# Edit with your API key
GEMINI_API_KEY=your_gemini_api_key_here
```

### Usage

#### Command Line
```bash
# Run EchoGem
echogem

# Or run directly with Python
python main.py
```

#### As a Python Package
```python
from echogem import GeminiAgent

# Initialize the agent
agent = GeminiAgent("your_api_key")

# Get a response
response = agent.respond("Hello, how are you?")
print(response)
```

## 🎮 Interactive Commands

Once EchoGem is running, you can use these commands:

| Command | Description |
|---------|-------------|
| `exit` | Quit the application |
| `clear` | Clear conversation history |
| `mode` | Change response mode (sync/async/streaming) |
| `info` | Show model and session information |
| `history` | Display saved conversation history |
| `save` | Save current conversation with custom title |
| `config` | Show current configuration |
| `help` | Display help message |

## 🔧 Development

### Setting Up Development Environment

```bash
# Clone and install
git clone https://github.com/asadullah48/EchoGem.git
cd EchoGem

# Install development dependencies
make install-dev

# Or manually
pip install -e ".[dev,test]"
```

### Running Tests

```bash
# Run all tests
make test

# Run with coverage
make test-coverage

# Run only unit tests
make test-unit

# Run linting
make lint

# Format code
make format
```

### Development Workflow

```bash
# Quick development cycle
make quick

# Full pre-commit checks
make pre-commit

# Clean build artifacts
make clean
```

## 📁 Project Structure

```
echogem/
├── echogem/                 # Main package
│   ├── __init__.py         # Package initialization
│   ├── agent.py            # GeminiAgent class
│   ├── cli.py              # Command-line interface
│   ├── config.py           # Configuration constants
│   └── utils.py            # Utility functions
├── tests/                   # Test suite
│   ├── test_agent.py       # Agent tests
│   ├── test_cli.py         # CLI tests
│   ├── test_config.py      # Configuration tests
│   └── test_utils.py       # Utility tests
├── main.py                 # Entry point
├── requirements.txt        # Core dependencies
├── requirements-dev.txt    # Development dependencies
├── pyproject.toml         # Project configuration
├── setup.py               # Setup script
├── Makefile               # Development commands
└── README.md              # This file
```

## ⚙️ Configuration

EchoGem supports various configuration options:

### Environment Variables
- `GEMINI_API_KEY`: Your Google Gemini API key (required)
- `GEMINI_MODEL`: Model to use (optional, defaults to `gemini-2.0-flash-exp`)
- `LOG_LEVEL`: Logging level (optional, defaults to `INFO`)

### Configuration File
EchoGem automatically creates a configuration file at:
- **Windows**: `%APPDATA%\echogem\config.json`
- **macOS/Linux**: `~/.local/share/echogem/config.json`

Available settings:
```json
{
  "default_model": "gemini-2.0-flash-exp",
  "default_mode": 1,
  "save_conversations": true,
  "max_conversations": 10,
  "temperature": 0.7,
  "max_output_tokens": 8192
}
```

## 🧪 Testing

EchoGem includes a comprehensive test suite:

```bash
# Run all tests
python run_tests.py

# Run with specific options
python run_tests.py --coverage --verbose
python run_tests.py --unit --fast
python run_tests.py --lint --format
```

### Test Coverage
The project maintains high test coverage across all modules:
- Agent functionality
- CLI interface
- Configuration management
- Utility functions

## 📚 API Reference

### GeminiAgent Class

```python
from echogem import GeminiAgent

# Initialize
agent = GeminiAgent(api_key="your_key", model_name="gemini-2.0-flash-exp")

# Synchronous response
response = agent.respond("Hello!")

# Asynchronous response
response = await agent.respond_async("Hello!")

# Streaming response
for chunk in agent.respond_stream("Hello!"):
    print(chunk, end="")

# Clear conversation history
agent.clear_history()

# Get model information
info = agent.get_model_info()
```

### Utility Functions

```python
from echogem.utils import validate_api_key, ConversationHistory, ConfigManager

# Validate API key format
is_valid = validate_api_key("AIzaSy...")

# Manage conversation history
history = ConversationHistory()
history.add_exchange("Hello", "Hi there!")
history.save_conversation("My Chat")

# Manage configuration
config = ConfigManager()
config.set("temperature", 0.8)
temp = config.get("temperature")
```

## 🤝 Contributing

We welcome contributions! Please see our contributing guidelines:

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Run tests (`make test`)
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

### Development Guidelines
- Follow PEP 8 style guidelines
- Write comprehensive tests for new features
- Update documentation as needed
- Use type hints where appropriate

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Google AI** for the powerful Gemini API
- **Created by** [Asadullah](https://github.com/asadullah48) - Original creator and maintainer
- **Contributors** who help make EchoGem better

## 📞 Support

- 🐛 **Bug Reports**: [GitHub Issues](https://github.com/asadullah48/EchoGem/issues)
- 💡 **Feature Requests**: [GitHub Discussions](https://github.com/asadullah48/EchoGem/discussions)
- 📖 **Documentation**: [GitHub Wiki](https://github.com/asadullah48/EchoGem/wiki)

---

<div align="center">
  <strong>Made with ❤️ by <a href="https://github.com/asadullah48">Asadullah</a></strong>
</div>
