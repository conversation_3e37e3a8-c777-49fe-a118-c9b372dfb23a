[project]
name = "echogem"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "annotated>=0.0.2",
    "cachetools>=6.1.0",
    "certifi>=2025.6.15",
    "charset-normalizer>=3.4.2",
    "colorama>=0.4.6",
    "google-api-core>=1.16.0",
    "google-api-python-client>=1.4.2",
    "google-auth>=1.6.3",
    "google-auth-httplib2>=0.2.0",
    "google-common-protos>=1.27.1",
    "grpcio>=1.73.1",
    "grpcio-status>=1.73.1",
    "httplib2>=0.22.0",
    "idna>=3.10",
    "proto-plus>=1.26.1",
    "protobuf>=6.31.1",
    "pyasn1>=0.6.1",
    "pyasn1-modules>=0.4.2",
    "pydantic>=2.11.7",
    "pydantic-core>=2.33.2",
    "pyparsing>=3.2.3",
    "python-dotenv>=1.1.1",
    "requests>=2.32.4",
    "rsa>=4.9.1",
    "tqdm>=4.67.1",
    "typing-extensions>=4.14.1",
    "typing-inspection>=0.4.1",
    "uritemplate>=4.2.0",
    "urllib3>=2.5.0",
    "google-generativeai>=0.8.4",
]
