"""
Tests for the utils module.
"""

import pytest
import tempfile
import json
from pathlib import Path
from datetime import datetime
from unittest.mock import patch, mock_open

from echogem.utils import (
    ConversationHistory,
    ConfigManager,
    format_timestamp,
    truncate_text,
    validate_api_key,
    get_app_data_dir,
    ensure_app_data_dir,
)


class TestUtilityFunctions:
    """Test utility functions."""
    
    def test_format_timestamp(self):
        """Test timestamp formatting."""
        # Test valid ISO timestamp
        timestamp = "2024-01-15T10:30:45.123456"
        formatted = format_timestamp(timestamp)
        assert "2024-01-15 10:30:45" in formatted
        
        # Test invalid timestamp
        invalid_timestamp = "invalid"
        formatted = format_timestamp(invalid_timestamp)
        assert formatted == invalid_timestamp
    
    def test_truncate_text(self):
        """Test text truncation."""
        # Test short text
        short_text = "Hello"
        assert truncate_text(short_text, 100) == short_text
        
        # Test long text
        long_text = "A" * 150
        truncated = truncate_text(long_text, 100)
        assert len(truncated) == 100
        assert truncated.endswith("...")
        
        # Test exact length
        exact_text = "A" * 100
        assert truncate_text(exact_text, 100) == exact_text
    
    def test_validate_api_key(self):
        """Test API key validation."""
        # Test valid API key
        valid_key = "AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI"
        assert validate_api_key(valid_key) == True
        
        # Test invalid API keys
        assert validate_api_key("") == False
        assert validate_api_key(None) == False
        assert validate_api_key("invalid_key") == False
        assert validate_api_key("AIza") == False  # Too short
        assert validate_api_key("AIza" + "A" * 100) == False  # Too long
    
    @patch('echogem.utils.Path.home')
    def test_get_app_data_dir_unix(self, mock_home):
        """Test app data directory on Unix systems."""
        mock_home.return_value = Path("/home/<USER>")
        
        with patch('os.name', 'posix'):
            app_dir = get_app_data_dir()
            expected = Path("/home/<USER>/.local/share/echogem")
            assert app_dir == expected
    
    @patch.dict('os.environ', {'APPDATA': 'C:\\Users\\<USER>\\AppData\\Roaming'})
    def test_get_app_data_dir_windows(self):
        """Test app data directory on Windows."""
        with patch('os.name', 'nt'):
            app_dir = get_app_data_dir()
            expected = Path("C:\\Users\\<USER>\\AppData\\Roaming\\echogem")
            assert app_dir == expected


class TestConversationHistory:
    """Test conversation history management."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.history_file = Path(self.temp_dir) / "conversations.json"
        
        # Mock the app data directory
        with patch('echogem.utils.ensure_app_data_dir', return_value=Path(self.temp_dir)):
            self.history = ConversationHistory(max_conversations=3)
    
    def test_add_exchange(self):
        """Test adding exchanges to conversation."""
        self.history.add_exchange("Hello", "Hi there!")
        
        assert len(self.history.current_conversation) == 1
        exchange = self.history.current_conversation[0]
        
        assert exchange['user'] == "Hello"
        assert exchange['ai'] == "Hi there!"
        assert 'timestamp' in exchange
    
    def test_clear_current_conversation(self):
        """Test clearing current conversation."""
        self.history.add_exchange("Hello", "Hi there!")
        assert len(self.history.current_conversation) == 1
        
        self.history.clear_current_conversation()
        assert len(self.history.current_conversation) == 0
    
    def test_get_conversation_summary_empty(self):
        """Test conversation summary when empty."""
        summary = self.history.get_conversation_summary()
        assert "No conversation in progress" in summary
    
    def test_get_conversation_summary_with_exchanges(self):
        """Test conversation summary with exchanges."""
        self.history.add_exchange("Hello", "Hi there!")
        summary = self.history.get_conversation_summary()
        
        assert "1 exchanges" in summary
        assert "started at" in summary
    
    def test_save_and_load_conversations(self):
        """Test saving and loading conversations."""
        # Add some exchanges
        self.history.add_exchange("Hello", "Hi there!")
        self.history.add_exchange("How are you?", "I'm doing well!")
        
        # Save conversation
        self.history.save_conversation("Test conversation")
        
        # Load conversations
        conversations = self.history.load_conversations()
        
        assert len(conversations) == 1
        conv = conversations[0]
        assert conv['title'] == "Test conversation"
        assert len(conv['exchanges']) == 2
        assert conv['exchanges'][0]['user'] == "Hello"
    
    def test_max_conversations_limit(self):
        """Test that only max_conversations are kept."""
        # Create more conversations than the limit
        for i in range(5):
            self.history.add_exchange(f"Message {i}", f"Response {i}")
            self.history.save_conversation(f"Conversation {i}")
            self.history.clear_current_conversation()
        
        conversations = self.history.load_conversations()
        assert len(conversations) == 3  # max_conversations = 3


class TestConfigManager:
    """Test configuration management."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = Path(self.temp_dir) / "config.json"
        
        # Mock the app data directory
        with patch('echogem.utils.ensure_app_data_dir', return_value=Path(self.temp_dir)):
            self.config_manager = ConfigManager()
    
    def test_default_config(self):
        """Test default configuration values."""
        assert self.config_manager.get('default_model') == 'gemini-2.0-flash-exp'
        assert self.config_manager.get('default_mode') == 1
        assert self.config_manager.get('save_conversations') == True
        assert self.config_manager.get('max_conversations') == 10
    
    def test_get_and_set(self):
        """Test getting and setting configuration values."""
        # Test getting existing value
        assert self.config_manager.get('temperature') == 0.7
        
        # Test getting non-existing value with default
        assert self.config_manager.get('non_existing', 'default') == 'default'
        
        # Test setting value
        self.config_manager.set('test_key', 'test_value')
        assert self.config_manager.get('test_key') == 'test_value'
    
    def test_save_and_load_config(self):
        """Test saving and loading configuration."""
        # Set a custom value
        self.config_manager.set('custom_setting', 'custom_value')
        
        # Create a new config manager (simulates restart)
        with patch('echogem.utils.ensure_app_data_dir', return_value=Path(self.temp_dir)):
            new_config_manager = ConfigManager()
        
        # Check that the custom value persists
        assert new_config_manager.get('custom_setting') == 'custom_value'
