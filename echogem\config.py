"""
Configuration settings for EchoGem.

This module contains configuration constants and settings used throughout the application.
"""

import os
from typing import Dict, Any

# Default model configurations
DEFAULT_MODEL = "gemini-2.0-flash-exp"
FALLBACK_MODEL = "gemini-1.5-flash"

# Generation configuration
GENERATION_CONFIG = {
    "temperature": 0.7,
    "top_p": 0.95,
    "top_k": 40,
    "max_output_tokens": 8192,
}

# Safety settings for content filtering
SAFETY_SETTINGS = [
    {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
    {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
    {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
    {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
]

# Application settings
APP_NAME = "EchoGem"
APP_VERSION = "1.0.0"
APP_DESCRIPTION = "A powerful CLI chatbot powered by Google Gemini AI"

# Environment variable names
ENV_API_KEY = "GEMINI_API_KEY"

# Logging configuration
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_LEVEL = "INFO"

def get_model_config(model_name: str = None) -> Dict[str, Any]:
    """Get model configuration for the specified model."""
    return {
        "model_name": model_name or DEFAULT_MODEL,
        "generation_config": GENERATION_CONFIG,
        "safety_settings": SAFETY_SETTINGS,
    }
