# Makefile for EchoGem development

.PHONY: help install install-dev test test-unit test-integration test-coverage lint format clean build upload docs run

# Default target
help:
	@echo "EchoGem Development Commands"
	@echo "============================"
	@echo ""
	@echo "Setup:"
	@echo "  install      Install the package"
	@echo "  install-dev  Install development dependencies"
	@echo ""
	@echo "Testing:"
	@echo "  test         Run all tests"
	@echo "  test-unit    Run unit tests only"
	@echo "  test-integration  Run integration tests only"
	@echo "  test-coverage     Run tests with coverage report"
	@echo ""
	@echo "Code Quality:"
	@echo "  lint         Run linting checks (flake8, mypy)"
	@echo "  format       Format code (black, isort)"
	@echo "  format-check Check code formatting without changes"
	@echo ""
	@echo "Development:"
	@echo "  run          Run the application"
	@echo "  clean        Clean build artifacts"
	@echo "  build        Build distribution packages"
	@echo "  docs         Generate documentation"
	@echo ""
	@echo "Release:"
	@echo "  upload       Upload to PyPI (requires credentials)"

# Installation
install:
	pip install -e .

install-dev:
	pip install -e ".[dev,test]"
	pip install -r requirements-dev.txt

# Testing
test:
	python run_tests.py

test-unit:
	python run_tests.py --unit

test-integration:
	python run_tests.py --integration

test-coverage:
	python run_tests.py --coverage

test-fast:
	python run_tests.py --fast

# Code quality
lint:
	python run_tests.py --lint

format:
	python -m black echogem/ tests/ *.py
	python -m isort echogem/ tests/ *.py

format-check:
	python run_tests.py --format

# Development
run:
	python main.py

run-dev:
	python -m echogem.cli

# Build and distribution
clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf .pytest_cache/
	rm -rf htmlcov/
	rm -rf .coverage
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

build: clean
	python setup.py sdist bdist_wheel

upload: build
	twine upload dist/*

# Documentation
docs:
	@echo "Generating documentation..."
	@echo "Documentation will be available in docs/ directory"

# Development workflow
dev-setup: install-dev
	@echo "Development environment setup complete!"
	@echo "Run 'make test' to verify everything works"

dev-check: format-check lint test-fast
	@echo "All development checks passed!"

# CI/CD targets
ci-test: test-coverage lint format-check
	@echo "CI tests completed"

# Quick development cycle
quick: format lint test-fast
	@echo "Quick development cycle completed"

# Full check before commit
pre-commit: format lint test
	@echo "Pre-commit checks completed successfully!"
