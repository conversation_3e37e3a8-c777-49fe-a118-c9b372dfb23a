#!/usr/bin/env python3
"""
Test runner script for EchoGem.

This script provides an easy way to run tests with different configurations.
"""

import sys
import subprocess
import argparse
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(command)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        return True
    except subprocess.CalledProcessError as e:
        print(f"ERROR: {description} failed with exit code {e.returncode}")
        print("STDOUT:", e.stdout)
        print("STDERR:", e.stderr)
        return False
    except FileNotFoundError:
        print(f"ERROR: Command not found. Make sure the required tools are installed.")
        return False


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(description="Run tests for EchoGem")
    parser.add_argument("--unit", action="store_true", help="Run only unit tests")
    parser.add_argument("--integration", action="store_true", help="Run only integration tests")
    parser.add_argument("--coverage", action="store_true", help="Run tests with coverage report")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--fast", action="store_true", help="Skip slow tests")
    parser.add_argument("--lint", action="store_true", help="Run linting checks")
    parser.add_argument("--format", action="store_true", help="Run code formatting")
    parser.add_argument("--all", action="store_true", help="Run all checks (tests, lint, format)")
    
    args = parser.parse_args()
    
    # Ensure we're in the project root
    project_root = Path(__file__).parent
    if not (project_root / "echogem").exists():
        print("ERROR: Please run this script from the project root directory")
        sys.exit(1)
    
    success = True
    
    # Code formatting
    if args.format or args.all:
        print("\n🎨 Running code formatting...")
        
        # Black formatting
        if not run_command(
            ["python", "-m", "black", "echogem/", "tests/", "--check"],
            "Black code formatting check"
        ):
            print("💡 To fix formatting issues, run: python -m black echogem/ tests/")
            success = False
        
        # isort import sorting
        if not run_command(
            ["python", "-m", "isort", "echogem/", "tests/", "--check-only"],
            "isort import sorting check"
        ):
            print("💡 To fix import sorting, run: python -m isort echogem/ tests/")
            success = False
    
    # Linting
    if args.lint or args.all:
        print("\n🔍 Running linting checks...")
        
        # Flake8 linting
        if not run_command(
            ["python", "-m", "flake8", "echogem/", "tests/"],
            "Flake8 linting"
        ):
            success = False
        
        # MyPy type checking
        if not run_command(
            ["python", "-m", "mypy", "echogem/"],
            "MyPy type checking"
        ):
            success = False
    
    # Testing
    if not args.format and not args.lint:  # Run tests unless only formatting/linting requested
        print("\n🧪 Running tests...")
        
        # Build pytest command
        pytest_cmd = ["python", "-m", "pytest"]
        
        if args.verbose:
            pytest_cmd.append("-v")
        
        if args.fast:
            pytest_cmd.extend(["-m", "not slow"])
        
        if args.unit:
            pytest_cmd.extend(["-m", "unit"])
        elif args.integration:
            pytest_cmd.extend(["-m", "integration"])
        
        if args.coverage:
            pytest_cmd.extend([
                "--cov=echogem",
                "--cov-report=html",
                "--cov-report=term-missing",
                "--cov-fail-under=80"
            ])
        
        pytest_cmd.append("tests/")
        
        if not run_command(pytest_cmd, "Running tests"):
            success = False
        
        if args.coverage:
            print("\n📊 Coverage report generated in htmlcov/index.html")
    
    # Summary
    print(f"\n{'='*60}")
    if success:
        print("✅ All checks passed!")
        sys.exit(0)
    else:
        print("❌ Some checks failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
