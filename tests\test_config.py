"""
Tests for the configuration module.
"""

import pytest
from echogem.config import (
    DEFAULT_MODEL,
    FALLBACK_MODEL,
    GENERATION_CONFIG,
    SAFETY_SETTINGS,
    get_model_config,
    APP_NAME,
    APP_VERSION,
    ENV_API_KEY,
)


class TestConfig:
    """Test configuration constants and functions."""
    
    def test_default_model(self):
        """Test that default model is set correctly."""
        assert DEFAULT_MODEL == "gemini-2.0-flash-exp"
        assert isinstance(DEFAULT_MODEL, str)
        assert len(DEFAULT_MODEL) > 0
    
    def test_fallback_model(self):
        """Test that fallback model is set correctly."""
        assert FALLBACK_MODEL == "gemini-1.5-flash"
        assert isinstance(FALLBACK_MODEL, str)
        assert len(FALLBACK_MODEL) > 0
    
    def test_generation_config(self):
        """Test generation configuration structure."""
        assert isinstance(GENERATION_CONFIG, dict)
        
        # Check required keys
        required_keys = ["temperature", "top_p", "top_k", "max_output_tokens"]
        for key in required_keys:
            assert key in GENERATION_CONFIG
        
        # Check value types and ranges
        assert isinstance(GENERATION_CONFIG["temperature"], (int, float))
        assert 0 <= GENERATION_CONFIG["temperature"] <= 2
        
        assert isinstance(GENERATION_CONFIG["top_p"], (int, float))
        assert 0 <= GENERATION_CONFIG["top_p"] <= 1
        
        assert isinstance(GENERATION_CONFIG["top_k"], int)
        assert GENERATION_CONFIG["top_k"] > 0
        
        assert isinstance(GENERATION_CONFIG["max_output_tokens"], int)
        assert GENERATION_CONFIG["max_output_tokens"] > 0
    
    def test_safety_settings(self):
        """Test safety settings structure."""
        assert isinstance(SAFETY_SETTINGS, list)
        assert len(SAFETY_SETTINGS) > 0
        
        # Check each safety setting
        for setting in SAFETY_SETTINGS:
            assert isinstance(setting, dict)
            assert "category" in setting
            assert "threshold" in setting
            assert isinstance(setting["category"], str)
            assert isinstance(setting["threshold"], str)
    
    def test_app_constants(self):
        """Test application constants."""
        assert APP_NAME == "EchoGem"
        assert isinstance(APP_VERSION, str)
        assert ENV_API_KEY == "GEMINI_API_KEY"
    
    def test_get_model_config_default(self):
        """Test get_model_config with default parameters."""
        config = get_model_config()
        
        assert isinstance(config, dict)
        assert "model_name" in config
        assert "generation_config" in config
        assert "safety_settings" in config
        
        assert config["model_name"] == DEFAULT_MODEL
        assert config["generation_config"] == GENERATION_CONFIG
        assert config["safety_settings"] == SAFETY_SETTINGS
    
    def test_get_model_config_custom_model(self):
        """Test get_model_config with custom model name."""
        custom_model = "gemini-1.5-pro"
        config = get_model_config(custom_model)
        
        assert config["model_name"] == custom_model
        assert config["generation_config"] == GENERATION_CONFIG
        assert config["safety_settings"] == SAFETY_SETTINGS
    
    def test_get_model_config_none_model(self):
        """Test get_model_config with None model name."""
        config = get_model_config(None)
        assert config["model_name"] == DEFAULT_MODEL
    
    def test_get_model_config_empty_model(self):
        """Test get_model_config with empty model name."""
        config = get_model_config("")
        assert config["model_name"] == DEFAULT_MODEL
