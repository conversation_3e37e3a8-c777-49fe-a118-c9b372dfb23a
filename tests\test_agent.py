"""
Tests for the agent module.
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from echogem.agent import GeminiAgent


class TestGeminiAgent:
    """Test the GeminiAgent class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.api_key = "AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI"
        self.model_name = "gemini-2.0-flash-exp"
    
    @patch('echogem.agent.genai.configure')
    @patch('echogem.agent.genai.GenerativeModel')
    def test_initialization(self, mock_model_class, mock_configure):
        """Test agent initialization."""
        mock_model = Mock()
        mock_chat_session = Mock()
        mock_model.start_chat.return_value = mock_chat_session
        mock_model_class.return_value = mock_model
        
        agent = GeminiAgent(self.api_key, self.model_name)
        
        # Check that genai.configure was called with the API key
        mock_configure.assert_called_once_with(api_key=self.api_key)
        
        # Check that the model was created with correct parameters
        mock_model_class.assert_called_once()
        call_args = mock_model_class.call_args
        assert call_args[1]['model_name'] == self.model_name
        assert 'generation_config' in call_args[1]
        assert 'safety_settings' in call_args[1]
        
        # Check that chat session was started
        mock_model.start_chat.assert_called_once_with(history=[])
        
        # Check agent attributes
        assert agent.api_key == self.api_key
        assert agent.model == mock_model
        assert agent.chat_session == mock_chat_session
    
    @patch('echogem.agent.genai.configure')
    @patch('echogem.agent.genai.GenerativeModel')
    def test_initialization_with_default_model(self, mock_model_class, mock_configure):
        """Test agent initialization with default model."""
        mock_model = Mock()
        mock_model.start_chat.return_value = Mock()
        mock_model_class.return_value = mock_model
        
        agent = GeminiAgent(self.api_key)  # No model name specified
        
        # Check that default model was used
        call_args = mock_model_class.call_args
        assert 'gemini-2.0-flash-exp' in call_args[1]['model_name']
    
    @patch('echogem.agent.genai.configure')
    @patch('echogem.agent.genai.GenerativeModel')
    def test_initialization_failure(self, mock_model_class, mock_configure):
        """Test agent initialization failure."""
        mock_configure.side_effect = Exception("API key invalid")
        
        with pytest.raises(Exception, match="API key invalid"):
            GeminiAgent(self.api_key)
    
    @patch('echogem.agent.genai.configure')
    @patch('echogem.agent.genai.GenerativeModel')
    def test_respond_success(self, mock_model_class, mock_configure):
        """Test successful response generation."""
        # Set up mocks
        mock_response = Mock()
        mock_response.text = "Hello! How can I help you?"
        
        mock_chat_session = Mock()
        mock_chat_session.send_message.return_value = mock_response
        
        mock_model = Mock()
        mock_model.start_chat.return_value = mock_chat_session
        mock_model_class.return_value = mock_model
        
        # Create agent and test response
        agent = GeminiAgent(self.api_key)
        response = agent.respond("Hello")
        
        # Check that send_message was called correctly
        mock_chat_session.send_message.assert_called_once_with("Hello")
        assert response == "Hello! How can I help you?"
    
    @patch('echogem.agent.genai.configure')
    @patch('echogem.agent.genai.GenerativeModel')
    def test_respond_failure(self, mock_model_class, mock_configure):
        """Test response generation failure."""
        # Set up mocks
        mock_chat_session = Mock()
        mock_chat_session.send_message.side_effect = Exception("API error")
        
        mock_model = Mock()
        mock_model.start_chat.return_value = mock_chat_session
        mock_model_class.return_value = mock_model
        
        # Create agent and test response
        agent = GeminiAgent(self.api_key)
        response = agent.respond("Hello")
        
        # Check that error was handled gracefully
        assert "Sorry, I encountered an error" in response
        assert "API error" in response
    
    @patch('echogem.agent.genai.configure')
    @patch('echogem.agent.genai.GenerativeModel')
    def test_respond_no_chat_session(self, mock_model_class, mock_configure):
        """Test response when chat session is not initialized."""
        mock_model = Mock()
        mock_model.start_chat.return_value = None
        mock_model_class.return_value = mock_model
        
        agent = GeminiAgent(self.api_key)
        agent.chat_session = None  # Simulate missing chat session
        
        response = agent.respond("Hello")
        assert "Chat session not initialized" in response
    
    @patch('echogem.agent.genai.configure')
    @patch('echogem.agent.genai.GenerativeModel')
    @pytest.mark.asyncio
    async def test_respond_async_success(self, mock_model_class, mock_configure):
        """Test successful async response generation."""
        # Set up mocks
        mock_response = Mock()
        mock_response.text = "Async response"
        
        mock_model = Mock()
        mock_model.generate_content_async = AsyncMock(return_value=mock_response)
        mock_model.start_chat.return_value = Mock()
        mock_model_class.return_value = mock_model
        
        # Create agent and test async response
        agent = GeminiAgent(self.api_key)
        response = await agent.respond_async("Hello")
        
        # Check that generate_content_async was called correctly
        mock_model.generate_content_async.assert_called_once_with("Hello")
        assert response == "Async response"
    
    @patch('echogem.agent.genai.configure')
    @patch('echogem.agent.genai.GenerativeModel')
    def test_respond_stream_success(self, mock_model_class, mock_configure):
        """Test successful streaming response generation."""
        # Set up mocks
        mock_chunk1 = Mock()
        mock_chunk1.text = "Hello "
        mock_chunk2 = Mock()
        mock_chunk2.text = "there!"
        
        mock_response = [mock_chunk1, mock_chunk2]
        
        mock_chat_session = Mock()
        mock_chat_session.send_message.return_value = mock_response
        
        mock_model = Mock()
        mock_model.start_chat.return_value = mock_chat_session
        mock_model_class.return_value = mock_model
        
        # Create agent and test streaming response
        agent = GeminiAgent(self.api_key)
        chunks = list(agent.respond_stream("Hello"))
        
        # Check that send_message was called with stream=True
        mock_chat_session.send_message.assert_called_once_with("Hello", stream=True)
        assert chunks == ["Hello ", "there!"]
    
    @patch('echogem.agent.genai.configure')
    @patch('echogem.agent.genai.GenerativeModel')
    def test_clear_history(self, mock_model_class, mock_configure):
        """Test clearing conversation history."""
        mock_new_chat_session = Mock()
        
        mock_model = Mock()
        mock_model.start_chat.return_value = mock_new_chat_session
        mock_model_class.return_value = mock_model
        
        agent = GeminiAgent(self.api_key)
        old_chat_session = agent.chat_session
        
        agent.clear_history()
        
        # Check that a new chat session was created
        assert agent.chat_session == mock_new_chat_session
        assert agent.chat_session != old_chat_session
    
    @patch('echogem.agent.genai.configure')
    @patch('echogem.agent.genai.GenerativeModel')
    def test_get_model_info(self, mock_model_class, mock_configure):
        """Test getting model information."""
        mock_model = Mock()
        mock_model.start_chat.return_value = Mock()
        mock_model_class.return_value = mock_model
        
        agent = GeminiAgent(self.api_key, self.model_name)
        info = agent.get_model_info()
        
        assert isinstance(info, dict)
        assert info['model_name'] == self.model_name
        assert 'generation_config' in info
        assert info['has_chat_session'] == True
