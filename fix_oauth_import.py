#!/usr/bin/env python3
"""
Temporary fix for oauth2client import issue.

This script patches the oauth2client module to provide the missing util module.
"""

import sys
import os

def patch_oauth2client():
    """Patch oauth2client to provide missing util module."""
    try:
        import oauth2client
        
        # Check if util is already available
        if hasattr(oauth2client, 'util'):
            print("✅ oauth2client.util already available")
            return True
            
        # Create a minimal util module
        class UtilModule:
            """Minimal util module for oauth2client compatibility."""
            
            @staticmethod
            def positional(max_positional_args):
                """Decorator for positional arguments."""
                def decorator(func):
                    return func
                return decorator
            
            @staticmethod
            def scopes_to_string(scopes):
                """Convert scopes to string."""
                if isinstance(scopes, str):
                    return scopes
                return ' '.join(scopes)
            
            @staticmethod
            def string_to_scopes(scopes):
                """Convert string to scopes."""
                if isinstance(scopes, list):
                    return scopes
                return scopes.split(' ') if scopes else []
        
        # Add the util module to oauth2client
        oauth2client.util = UtilModule()
        
        print("✅ Successfully patched oauth2client.util")
        return True
        
    except ImportError:
        print("❌ oauth2client not found")
        return False
    except Exception as e:
        print(f"❌ Failed to patch oauth2client: {e}")
        return False

if __name__ == "__main__":
    success = patch_oauth2client()
    
    if success:
        try:
            import google.generativeai as genai
            print("✅ google.generativeai imported successfully after patch!")
        except Exception as e:
            print(f"❌ Still failed to import google.generativeai: {e}")
    else:
        print("❌ Patch failed")
