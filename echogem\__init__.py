"""
EchoGem - A powerful CLI chatbot powered by Google Gemini AI.

This package provides a simple yet powerful interface to interact with Google's
Gemini AI models through a command-line interface.
"""

__version__ = "1.0.0"
__author__ = "EchoGem Team"
__email__ = "<EMAIL>"
__description__ = "A powerful CLI chatbot powered by Google Gemini AI"

from .agent import GeminiAgent
from .config import (
    DEFAULT_MODEL,
    FALL<PERSON><PERSON>K_MODEL,
    GENERATION_CONFIG,
    SAFETY_SETTINGS,
    get_model_config,
    APP_NAME,
    APP_VERSION,
    ENV_API_KEY,
)
from .utils import (
    ConversationHistory,
    ConfigManager,
    validate_api_key,
    format_timestamp,
    truncate_text,
)

__all__ = [
    "GeminiAgent",
    "DEFAULT_MODEL",
    "FALLBACK_MODEL",
    "GENERATION_CONFIG",
    "SAFETY_SETTINGS",
    "get_model_config",
    "APP_NAME",
    "APP_VERSION",
    "ENV_API_KEY",
    "ConversationHistory",
    "ConfigManager",
    "validate_api_key",
    "format_timestamp",
    "truncate_text",
]
