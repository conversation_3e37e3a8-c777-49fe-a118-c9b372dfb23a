{"license": "Apache 2.0", "name": "google-api-python-client", "metadata_version": "2.0", "generator": "bdist_wheel (0.24.0)", "summary": "Google API Client Library for Python", "run_requires": [{"requires": ["httplib2 (>=0.8)", "oauth2client (>=1.4.6)", "six (>=1.6.1)", "uritemplate (>=0.6)"]}], "version": "1.4.2", "extensions": {"python.details": {"project_urls": {"Home": "http://github.com/google/google-api-python-client/"}, "document_names": {"description": "DESCRIPTION.rst"}, "contacts": [{"role": "author", "name": "Google Inc."}]}}, "keywords": ["google", "api", "client"], "classifiers": ["Programming Language :: Python :: 2", "Programming Language :: Python :: 2.6", "Programming Language :: Python :: 2.7", "Programming Language :: Python :: 3", "Programming Language :: Python :: 3.3", "Programming Language :: Python :: 3.4", "Development Status :: 5 - Production/Stable", "Intended Audience :: <PERSON><PERSON><PERSON>", "License :: OSI Approved :: Apache Software License", "Operating System :: OS Independent", "Topic :: Internet :: WWW/HTTP"], "extras": []}