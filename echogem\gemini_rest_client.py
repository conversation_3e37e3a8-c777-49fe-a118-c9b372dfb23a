#!/usr/bin/env python3
"""
Simple REST client for Google Gemini API.

This module provides a lightweight alternative to the google-generativeai SDK
to avoid dependency conflicts.
"""

import json
import logging
from typing import Optional, Dict, Any, Generator
import requests

logger = logging.getLogger(__name__)


class GeminiRestClient:
    """Simple REST client for Google Gemini API."""
    
    def __init__(self, api_key: str, model_name: str = "gemini-1.5-flash"):
        """Initialize the Gemini REST client.
        
        Args:
            api_key: Google API key for Gemini
            model_name: Name of the Gemini model to use
        """
        self.api_key = api_key
        self.model_name = model_name
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"
        self.conversation_history = []
        
    def _make_request(self, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Make a request to the Gemini API.
        
        Args:
            endpoint: API endpoint
            data: Request data
            
        Returns:
            Response data
            
        Raises:
            Exception: If the request fails
        """
        url = f"{self.base_url}/{endpoint}?key={self.api_key}"
        
        headers = {
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {e}")
            raise Exception(f"Failed to communicate with Gemini API: {e}")
    
    def generate_content(self, prompt: str) -> str:
        """Generate content using the Gemini API.
        
        Args:
            prompt: Input prompt
            
        Returns:
            Generated text response
        """
        # Add user message to history
        self.conversation_history.append({
            "role": "user",
            "parts": [{"text": prompt}]
        })
        
        # Prepare request data
        data = {
            "contents": self.conversation_history,
            "generationConfig": {
                "temperature": 0.7,
                "topK": 40,
                "topP": 0.95,
                "maxOutputTokens": 8192,
            },
            "safetySettings": [
                {
                    "category": "HARM_CATEGORY_HARASSMENT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    "category": "HARM_CATEGORY_HATE_SPEECH",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                }
            ]
        }
        
        try:
            # Make the API request
            endpoint = f"models/{self.model_name}:generateContent"
            response = self._make_request(endpoint, data)
            
            # Extract the generated text
            if "candidates" in response and len(response["candidates"]) > 0:
                candidate = response["candidates"][0]
                if "content" in candidate and "parts" in candidate["content"]:
                    generated_text = candidate["content"]["parts"][0]["text"]
                    
                    # Add assistant response to history
                    self.conversation_history.append({
                        "role": "model",
                        "parts": [{"text": generated_text}]
                    })
                    
                    return generated_text
                else:
                    raise Exception("No content in API response")
            else:
                raise Exception("No candidates in API response")
                
        except Exception as e:
            logger.error(f"Content generation failed: {e}")
            raise
    
    def generate_content_stream(self, prompt: str) -> Generator[str, None, None]:
        """Generate content with streaming response.
        
        Args:
            prompt: Input prompt
            
        Yields:
            Chunks of generated text
        """
        # For simplicity, we'll simulate streaming by yielding the full response
        # In a real implementation, you'd use the streaming API endpoint
        try:
            response = self.generate_content(prompt)
            
            # Simulate streaming by yielding chunks
            words = response.split()
            for i, word in enumerate(words):
                if i == 0:
                    yield word
                else:
                    yield f" {word}"
                    
        except Exception as e:
            logger.error(f"Streaming generation failed: {e}")
            yield f"Error: {e}"
    
    def clear_history(self):
        """Clear conversation history."""
        self.conversation_history = []
        logger.info("Conversation history cleared")
    
    def get_history_length(self) -> int:
        """Get the number of messages in conversation history.
        
        Returns:
            Number of messages
        """
        return len(self.conversation_history)
