"""
Gemini AI Agent implementation for EchoGem.

This module contains the GeminiAgent class that handles interactions with
Google's Gemini AI models.
"""

import logging
from typing import Op<PERSON>, Generator, AsyncGenerator
import google.generativeai as genai
from .config import get_model_config

logger = logging.getLogger(__name__)


class GeminiAgent:
    """A chatbot agent powered by Google Gemini AI."""
    
    def __init__(self, api_key: str, model_name: str = None):
        """
        Initialize the Gemini agent with API key and model.
        
        Args:
            api_key: Google Gemini API key
            model_name: Name of the Gemini model to use (optional)
        """
        self.api_key = api_key
        self.model_config = get_model_config(model_name)
        self.model = None
        self.chat_session = None
        self._initialize_model()
    
    def _initialize_model(self):
        """Initialize the Gemini model and configure it."""
        try:
            genai.configure(api_key=self.api_key)
            
            self.model = genai.GenerativeModel(
                model_name=self.model_config["model_name"],
                generation_config=self.model_config["generation_config"],
                safety_settings=self.model_config["safety_settings"]
            )
            
            # Start a chat session for conversation context
            self.chat_session = self.model.start_chat(history=[])
            logger.info(f"Successfully initialized Gemini model: {self.model_config['model_name']}")
            
        except Exception as e:
            logger.error(f"Failed to initialize Gemini model: {e}")
            raise
    
    def respond(self, user_input: str) -> str:
        """
        Generate a response to user input.
        
        Args:
            user_input: The user's message
            
        Returns:
            The AI's response as a string
        """
        try:
            if not self.chat_session:
                raise RuntimeError("Chat session not initialized")
            
            response = self.chat_session.send_message(user_input)
            return response.text
            
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return f"Sorry, I encountered an error: {str(e)}"
    
    async def respond_async(self, user_input: str) -> str:
        """
        Generate an async response to user input.
        
        Args:
            user_input: The user's message
            
        Returns:
            The AI's response as a string
        """
        try:
            if not self.model:
                raise RuntimeError("Model not initialized")
            
            response = await self.model.generate_content_async(user_input)
            return response.text
            
        except Exception as e:
            logger.error(f"Error generating async response: {e}")
            return f"Sorry, I encountered an error: {str(e)}"
    
    def respond_stream(self, user_input: str) -> Generator[str, None, None]:
        """
        Generate a streaming response to user input.
        
        Args:
            user_input: The user's message
            
        Yields:
            Chunks of the AI's response as strings
        """
        try:
            if not self.chat_session:
                raise RuntimeError("Chat session not initialized")
            
            response = self.chat_session.send_message(user_input, stream=True)
            for chunk in response:
                if chunk.text:
                    yield chunk.text
                    
        except Exception as e:
            logger.error(f"Error generating streaming response: {e}")
            yield f"Sorry, I encountered an error: {str(e)}"
    
    def clear_history(self):
        """Clear the conversation history by reinitializing the chat session."""
        try:
            self.chat_session = self.model.start_chat(history=[])
            logger.info("Conversation history cleared")
        except Exception as e:
            logger.error(f"Error clearing history: {e}")
            raise
    
    def get_model_info(self) -> dict:
        """
        Get information about the current model.
        
        Returns:
            Dictionary containing model information
        """
        return {
            "model_name": self.model_config["model_name"],
            "generation_config": self.model_config["generation_config"],
            "has_chat_session": self.chat_session is not None,
        }
