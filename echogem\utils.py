"""
Utility functions for EchoGem.

This module contains helper functions and utilities used throughout the application.
"""

import os
import json
import time
from datetime import datetime
from typing import Dict, List, Optional
from pathlib import Path


def get_app_data_dir() -> Path:
    """Get the application data directory."""
    if os.name == 'nt':  # Windows
        app_data = Path(os.environ.get('APPDATA', ''))
    else:  # Unix-like systems
        app_data = Path.home() / '.local' / 'share'
    
    return app_data / 'echogem'


def ensure_app_data_dir() -> Path:
    """Ensure the application data directory exists."""
    app_dir = get_app_data_dir()
    app_dir.mkdir(parents=True, exist_ok=True)
    return app_dir


class ConversationHistory:
    """Manages conversation history storage and retrieval."""
    
    def __init__(self, max_conversations: int = 10):
        """Initialize conversation history manager."""
        self.max_conversations = max_conversations
        self.app_dir = ensure_app_data_dir()
        self.history_file = self.app_dir / 'conversations.json'
        self.current_conversation = []
    
    def add_exchange(self, user_input: str, ai_response: str):
        """Add a user-AI exchange to the current conversation."""
        exchange = {
            'timestamp': datetime.now().isoformat(),
            'user': user_input,
            'ai': ai_response
        }
        self.current_conversation.append(exchange)
    
    def save_conversation(self, title: Optional[str] = None):
        """Save the current conversation to history."""
        if not self.current_conversation:
            return
        
        # Generate title if not provided
        if not title:
            first_user_input = self.current_conversation[0]['user']
            title = first_user_input[:50] + "..." if len(first_user_input) > 50 else first_user_input
        
        conversation = {
            'id': int(time.time()),
            'title': title,
            'timestamp': datetime.now().isoformat(),
            'exchanges': self.current_conversation.copy()
        }
        
        # Load existing conversations
        conversations = self.load_conversations()
        conversations.append(conversation)
        
        # Keep only the most recent conversations
        conversations = conversations[-self.max_conversations:]
        
        # Save to file
        try:
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(conversations, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Warning: Could not save conversation history: {e}")
    
    def load_conversations(self) -> List[Dict]:
        """Load conversation history from file."""
        try:
            if self.history_file.exists():
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"Warning: Could not load conversation history: {e}")
        
        return []
    
    def clear_current_conversation(self):
        """Clear the current conversation."""
        self.current_conversation = []
    
    def get_conversation_summary(self) -> str:
        """Get a summary of the current conversation."""
        if not self.current_conversation:
            return "No conversation in progress"
        
        exchange_count = len(self.current_conversation)
        start_time = self.current_conversation[0]['timestamp']
        
        return f"Current conversation: {exchange_count} exchanges, started at {start_time}"


class ConfigManager:
    """Manages application configuration."""
    
    def __init__(self):
        """Initialize configuration manager."""
        self.app_dir = ensure_app_data_dir()
        self.config_file = self.app_dir / 'config.json'
        self.default_config = {
            'default_model': 'gemini-2.0-flash-exp',
            'default_mode': 1,
            'save_conversations': True,
            'max_conversations': 10,
            'temperature': 0.7,
            'max_output_tokens': 8192,
        }
        self.config = self.load_config()
    
    def load_config(self) -> Dict:
        """Load configuration from file."""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # Merge with defaults to ensure all keys exist
                    return {**self.default_config, **config}
        except Exception as e:
            print(f"Warning: Could not load config: {e}")
        
        return self.default_config.copy()
    
    def save_config(self):
        """Save configuration to file."""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            print(f"Warning: Could not save config: {e}")
    
    def get(self, key: str, default=None):
        """Get a configuration value."""
        return self.config.get(key, default)
    
    def set(self, key: str, value):
        """Set a configuration value."""
        self.config[key] = value
        self.save_config()


def format_timestamp(timestamp_str: str) -> str:
    """Format a timestamp string for display."""
    try:
        dt = datetime.fromisoformat(timestamp_str)
        return dt.strftime("%Y-%m-%d %H:%M:%S")
    except Exception:
        return timestamp_str


def truncate_text(text: str, max_length: int = 100) -> str:
    """Truncate text to a maximum length."""
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."


def validate_api_key(api_key: str) -> bool:
    """Validate the format of a Gemini API key."""
    if not api_key:
        return False
    
    # Basic validation - Gemini API keys typically start with 'AIza'
    if not api_key.startswith('AIza'):
        return False
    
    # Check length (typical length is around 39 characters)
    if len(api_key) < 30 or len(api_key) > 50:
        return False
    
    return True
