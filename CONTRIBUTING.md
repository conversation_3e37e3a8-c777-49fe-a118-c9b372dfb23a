# Contributing to EchoGem

Thank you for your interest in contributing to EchoGem! This document provides guidelines and information for contributors.

## 🤝 How to Contribute

### Reporting Issues

Before creating an issue, please:

1. **Search existing issues** to avoid duplicates
2. **Use the issue templates** when available
3. **Provide detailed information** including:
   - Operating system and Python version
   - EchoGem version
   - Steps to reproduce the issue
   - Expected vs actual behavior
   - Error messages or logs

### Suggesting Features

We welcome feature suggestions! Please:

1. **Check existing feature requests** first
2. **Open a discussion** before implementing large features
3. **Provide clear use cases** and benefits
4. **Consider backwards compatibility**

### Contributing Code

#### Getting Started

1. **Fork the repository**
   ```bash
   git clone https://github.com/your-username/echogem.git
   cd echogem
   ```

2. **Set up development environment**
   ```bash
   make dev-setup
   # Or manually:
   pip install -e ".[dev,test]"
   ```

3. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

#### Development Workflow

1. **Make your changes**
   - Follow the coding standards below
   - Add tests for new functionality
   - Update documentation as needed

2. **Run tests and checks**
   ```bash
   make pre-commit
   # Or run individual checks:
   make test
   make lint
   make format-check
   ```

3. **Commit your changes**
   ```bash
   git add .
   git commit -m "feat: add amazing new feature"
   ```

4. **Push and create a Pull Request**
   ```bash
   git push origin feature/your-feature-name
   ```

## 📋 Coding Standards

### Code Style

We use several tools to maintain code quality:

- **Black** for code formatting
- **isort** for import sorting
- **Flake8** for linting
- **MyPy** for type checking

Run all checks with:
```bash
make dev-check
```

### Code Guidelines

1. **Follow PEP 8** style guidelines
2. **Use type hints** for all function parameters and return values
3. **Write docstrings** for all public functions and classes
4. **Keep functions small** and focused on a single responsibility
5. **Use meaningful variable names**
6. **Add comments** for complex logic

### Example Code Style

```python
from typing import Optional, List
import logging

logger = logging.getLogger(__name__)


class ExampleClass:
    """Example class demonstrating coding standards.
    
    This class shows the preferred style for EchoGem contributions.
    """
    
    def __init__(self, name: str, max_items: int = 10) -> None:
        """Initialize the example class.
        
        Args:
            name: The name of the instance
            max_items: Maximum number of items to store
        """
        self.name = name
        self.max_items = max_items
        self._items: List[str] = []
    
    def add_item(self, item: str) -> bool:
        """Add an item to the collection.
        
        Args:
            item: The item to add
            
        Returns:
            True if item was added, False if collection is full
        """
        if len(self._items) >= self.max_items:
            logger.warning(f"Cannot add item: collection full ({self.max_items})")
            return False
        
        self._items.append(item)
        logger.debug(f"Added item: {item}")
        return True
```

## 🧪 Testing

### Writing Tests

1. **Write tests for all new functionality**
2. **Use descriptive test names** that explain what is being tested
3. **Follow the AAA pattern**: Arrange, Act, Assert
4. **Mock external dependencies** (API calls, file system, etc.)
5. **Test both success and failure cases**

### Test Structure

```python
import pytest
from unittest.mock import Mock, patch
from echogem.agent import GeminiAgent


class TestGeminiAgent:
    """Test the GeminiAgent class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.api_key = "test_api_key"
        self.model_name = "test_model"
    
    @patch('echogem.agent.genai.configure')
    @patch('echogem.agent.genai.GenerativeModel')
    def test_initialization_success(self, mock_model_class, mock_configure):
        """Test successful agent initialization."""
        # Arrange
        mock_model = Mock()
        mock_model_class.return_value = mock_model
        
        # Act
        agent = GeminiAgent(self.api_key, self.model_name)
        
        # Assert
        mock_configure.assert_called_once_with(api_key=self.api_key)
        assert agent.api_key == self.api_key
```

### Running Tests

```bash
# Run all tests
make test

# Run specific test file
python -m pytest tests/test_agent.py

# Run with coverage
make test-coverage

# Run only fast tests
make test-fast
```

## 📚 Documentation

### Docstring Format

Use Google-style docstrings:

```python
def example_function(param1: str, param2: Optional[int] = None) -> bool:
    """Brief description of the function.
    
    Longer description if needed. Explain the purpose, behavior,
    and any important details.
    
    Args:
        param1: Description of the first parameter
        param2: Description of the optional second parameter
        
    Returns:
        Description of the return value
        
    Raises:
        ValueError: When param1 is empty
        RuntimeError: When operation fails
        
    Example:
        >>> result = example_function("hello", 42)
        >>> print(result)
        True
    """
```

### README Updates

When adding new features:

1. Update the feature list
2. Add usage examples
3. Update the API reference if needed
4. Add any new configuration options

## 🔄 Pull Request Process

### Before Submitting

1. **Ensure all tests pass**
2. **Update documentation** as needed
3. **Add changelog entry** if applicable
4. **Rebase on latest main** branch

### Pull Request Template

When creating a PR, please include:

- **Clear description** of changes
- **Link to related issues**
- **Testing information**
- **Breaking changes** (if any)
- **Screenshots** (for UI changes)

### Review Process

1. **Automated checks** must pass
2. **Code review** by maintainers
3. **Testing** on different platforms
4. **Documentation review**
5. **Final approval** and merge

## 🏷️ Commit Message Format

We follow conventional commits:

```
type(scope): description

[optional body]

[optional footer]
```

### Types
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

### Examples
```
feat(cli): add conversation history command
fix(agent): handle API timeout errors gracefully
docs(readme): update installation instructions
test(utils): add tests for configuration manager
```

## 🚀 Release Process

### Version Numbering

We follow [Semantic Versioning](https://semver.org/):

- **MAJOR**: Breaking changes
- **MINOR**: New features (backwards compatible)
- **PATCH**: Bug fixes (backwards compatible)

### Release Checklist

1. Update version numbers
2. Update CHANGELOG.md
3. Run full test suite
4. Create release tag
5. Build and upload to PyPI
6. Update documentation

## 🆘 Getting Help

If you need help:

1. **Check the documentation** first
2. **Search existing issues** and discussions
3. **Ask in GitHub Discussions**
4. **Join our community** (links in README)

## 📜 Code of Conduct

Please note that this project is released with a Contributor Code of Conduct. By participating in this project you agree to abide by its terms.

### Our Standards

- **Be respectful** and inclusive
- **Be constructive** in feedback
- **Focus on the code**, not the person
- **Help others learn** and grow

Thank you for contributing to EchoGem! 🎉
