import os
import sys
import asyncio
import logging
from typing import Optional
import google.generativeai as genai
from dotenv import load_dotenv
from colorama import init, Fore, Style

# Initialize colorama for cross-platform colored output
init()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class GeminiAgent:
    """A chatbot agent powered by Google Gemini AI."""

    def __init__(self, api_key: str, model_name: str = "gemini-2.0-flash-exp"):
        """Initialize the Gemini agent with API key and model."""
        self.api_key = api_key
        self.model_name = model_name
        self.model = None
        self.chat_session = None
        self._initialize_model()

    def _initialize_model(self):
        """Initialize the Gemini model and configure it."""
        try:
            genai.configure(api_key=self.api_key)

            # Configure the model with safety settings
            generation_config = {
                "temperature": 0.7,
                "top_p": 0.95,
                "top_k": 40,
                "max_output_tokens": 8192,
            }

            safety_settings = [
                {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
            ]

            self.model = genai.GenerativeModel(
                model_name=self.model_name,
                generation_config=generation_config,
                safety_settings=safety_settings
            )

            # Start a chat session for conversation context
            self.chat_session = self.model.start_chat(history=[])
            logger.info(f"Successfully initialized Gemini model: {self.model_name}")

        except Exception as e:
            logger.error(f"Failed to initialize Gemini model: {e}")
            raise

    def respond(self, user_input: str) -> str:
        """Generate a response to user input."""
        try:
            if not self.chat_session:
                raise RuntimeError("Chat session not initialized")

            response = self.chat_session.send_message(user_input)
            return response.text

        except Exception as e:
            logger.error(f"Error generating response: {e}")
            
            return f"Sorry, I encountered an error: {str(e)}"

    async def respond_async(self, user_input: str) -> str:
        """Generate an async response to user input."""
        try:
            if not self.model:
                raise RuntimeError("Model not initialized")

            response = await self.model.generate_content_async(user_input)
            return response.text

        except Exception as e:
            logger.error(f"Error generating async response: {e}")
            return f"Sorry, I encountered an error: {str(e)}"

    def respond_stream(self, user_input: str):
        """Generate a streaming response to user input."""
        try:
            if not self.chat_session:
                raise RuntimeError("Chat session not initialized")

            response = self.chat_session.send_message(user_input, stream=True)
            for chunk in response:
                if chunk.text:
                    yield chunk.text

        except Exception as e:
            logger.error(f"Error generating streaming response: {e}")
            yield f"Sorry, I encountered an error: {str(e)}"

def load_environment():
    """Load environment variables and validate API key."""
    load_dotenv()
    api_key = os.getenv("GEMINI_API_KEY")

    if not api_key:
        print(f"{Fore.RED}Error: GEMINI_API_KEY not found in environment variables.{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}Please create a .env file with your Gemini API key:{Style.RESET_ALL}")
        print("GEMINI_API_KEY=your_api_key_here")
        sys.exit(1)

    return api_key

def print_welcome():
    """Print welcome message and instructions."""
    print(f"\n{Fore.CYAN}🤖 Welcome to EchoGem - Your Gemini AI Assistant!{Style.RESET_ALL}")
    print(f"{Fore.GREEN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}Available modes:{Style.RESET_ALL}")
    print(f"  {Fore.WHITE}1{Style.RESET_ALL} - Sync Response (default)")
    print(f"  {Fore.WHITE}2{Style.RESET_ALL} - Async Response")
    print(f"  {Fore.WHITE}3{Style.RESET_ALL} - Streaming Response")
    print(f"\n{Fore.YELLOW}Commands:{Style.RESET_ALL}")
    print(f"  {Fore.WHITE}exit{Style.RESET_ALL} - Quit the application")
    print(f"  {Fore.WHITE}clear{Style.RESET_ALL} - Clear conversation history")
    print(f"  {Fore.WHITE}mode{Style.RESET_ALL} - Change response mode")
    print(f"{Fore.GREEN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━{Style.RESET_ALL}\n")

def get_response_mode():
    """Get the response mode from user."""
    while True:
        try:
            print(f"{Fore.CYAN}Choose response mode:{Style.RESET_ALL}")
            print("1. Sync Response")
            print("2. Async Response")
            print("3. Streaming Response")

            choice = input(f"{Fore.YELLOW}Enter mode (1-3, default=1): {Style.RESET_ALL}").strip()

            if not choice:
                return 1

            mode = int(choice)
            if mode in [1, 2, 3]:
                return mode
            else:
                print(f"{Fore.RED}Please enter 1, 2, or 3{Style.RESET_ALL}")

        except ValueError:
            print(f"{Fore.RED}Please enter a valid number{Style.RESET_ALL}")

async def handle_async_response(chatbot: GeminiAgent, user_input: str):
    """Handle async response mode."""
    print(f"{Fore.BLUE}🤖 Thinking...{Style.RESET_ALL}")
    response = await chatbot.respond_async(user_input)
    print(f"{Fore.GREEN}🤖 EchoGem:{Style.RESET_ALL} {response}")

def handle_streaming_response(chatbot: GeminiAgent, user_input: str):
    """Handle streaming response mode."""
    print(f"{Fore.GREEN}🤖 EchoGem:{Style.RESET_ALL} ", end="", flush=True)

    for chunk in chatbot.respond_stream(user_input):
        print(chunk, end="", flush=True)
    print()  # New line after streaming

async def main():
    """Main application loop."""
    try:
        # Load environment and initialize
        api_key = load_environment()
        print_welcome()

        # Initialize chatbot
        print(f"{Fore.BLUE}Initializing Gemini AI...{Style.RESET_ALL}")
        chatbot = GeminiAgent(api_key)
        print(f"{Fore.GREEN}✅ Ready to chat!{Style.RESET_ALL}\n")

        # Get initial response mode
        response_mode = get_response_mode()
        print()

        # Main chat loop
        while True:
            try:
                user_input = input(f"{Fore.CYAN}👤 You: {Style.RESET_ALL}").strip()

                if not user_input:
                    continue

                # Handle commands
                if user_input.lower() == 'exit':
                    print(f"{Fore.YELLOW}👋 Goodbye!{Style.RESET_ALL}")
                    break
                elif user_input.lower() == 'clear':
                    chatbot._initialize_model()  # Restart chat session
                    print(f"{Fore.GREEN}✅ Conversation history cleared{Style.RESET_ALL}")
                    continue
                elif user_input.lower() == 'mode':
                    response_mode = get_response_mode()
                    print()
                    continue

                # Generate response based on mode
                if response_mode == 1:
                    # Sync response
                    response = chatbot.respond(user_input)
                    print(f"{Fore.GREEN}🤖 EchoGem:{Style.RESET_ALL} {response}")

                elif response_mode == 2:
                    # Async response
                    await handle_async_response(chatbot, user_input)

                elif response_mode == 3:
                    # Streaming response
                    handle_streaming_response(chatbot, user_input)

                print()  # Add spacing between exchanges

            except KeyboardInterrupt:
                print(f"\n{Fore.YELLOW}👋 Goodbye!{Style.RESET_ALL}")
                break
            except Exception as e:
                logger.error(f"Error in main loop: {e}")
                print(f"{Fore.RED}An error occurred: {e}{Style.RESET_ALL}")

    except Exception as e:
        logger.error(f"Fatal error: {e}")
        print(f"{Fore.RED}Fatal error: {e}{Style.RESET_ALL}")
        sys.exit(1)

if __name__ == '__main__':
    asyncio.run(main())
