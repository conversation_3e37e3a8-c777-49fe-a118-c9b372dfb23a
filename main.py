import os
import logging
from google.oauth2 import service_account
from googleapiclient.discovery import build
from config import config

# Set up logging
logging.basicConfig(level=logging.INFO)

# Load API credentials
creds = service_account.Credentials.from_service_account_file(
    'path/to/credentials.json'
)

# Create API client
gemini_api = build('gemini', 'v1', credentials=creds)

def main():
    # Initialize chatbot
    chatbot = GeminiAgent(gemini_api)

    # Run chatbot
    while True:
        user_input = input('User: ')
        response = chatbot.respond(user_input)
        print('Chatbot:', response)

if __name__ == '__main__':
    main()
