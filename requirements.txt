# Core dependencies
google-generativeai==0.8.5
python-dotenv>=1.0.0
colorama>=0.4.6
typing-extensions>=4.8.0
# Required dependencies for google-generativeai
google-auth>=2.0.0
google-api-core>=2.0.0
grpcio>=1.60.0
protobuf>=4.25.0
requests>=2.25.0
tqdm>=4.60.0
pydantic>=2.0.0

# Development dependencies (optional)
# Install with: pip install -r requirements-dev.txt
# pytest>=8.0.0
# pytest-asyncio>=0.23.0
# pytest-cov>=4.0.0
# black>=24.0.0
# isort>=5.13.0
# flake8>=7.0.0
# mypy>=1.8.0