"""
Tests for the CLI module.
"""

import pytest
from unittest.mock import Mo<PERSON>, patch, MagicMock
from echogem.cli import EchoGemCLI


class TestEchoGemCLI:
    """Test the EchoGemCLI class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        with patch('echogem.cli.ConfigManager'), \
             patch('echogem.cli.ConversationHistory'):
            self.cli = EchoGemCLI()
    
    @patch('echogem.cli.load_dotenv')
    @patch('echogem.cli.os.getenv')
    @patch('echogem.cli.validate_api_key')
    def test_load_environment_success(self, mock_validate, mock_getenv, mock_load_dotenv):
        """Test successful environment loading."""
        mock_getenv.return_value = "AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI"
        mock_validate.return_value = True
        
        api_key = self.cli.load_environment()
        
        mock_load_dotenv.assert_called_once()
        mock_getenv.assert_called_once_with('GEMINI_API_KEY')
        assert api_key == "AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI"
    
    @patch('echogem.cli.load_dotenv')
    @patch('echogem.cli.os.getenv')
    @patch('echogem.cli.sys.exit')
    def test_load_environment_missing_key(self, mock_exit, mock_getenv, mock_load_dotenv):
        """Test environment loading with missing API key."""
        mock_getenv.return_value = None
        
        self.cli.load_environment()
        
        mock_exit.assert_called_once_with(1)
    
    @patch('echogem.cli.load_dotenv')
    @patch('echogem.cli.os.getenv')
    @patch('echogem.cli.validate_api_key')
    @patch('builtins.print')
    def test_load_environment_invalid_key(self, mock_print, mock_validate, mock_getenv, mock_load_dotenv):
        """Test environment loading with invalid API key."""
        mock_getenv.return_value = "invalid_key"
        mock_validate.return_value = False
        
        api_key = self.cli.load_environment()
        
        # Should still return the key but print a warning
        assert api_key == "invalid_key"
        mock_print.assert_called()
    
    @patch('builtins.input')
    def test_get_response_mode_default(self, mock_input):
        """Test getting response mode with default value."""
        mock_input.return_value = ""  # Empty input should default to 1
        
        mode = self.cli.get_response_mode()
        assert mode == 1
    
    @patch('builtins.input')
    def test_get_response_mode_valid(self, mock_input):
        """Test getting response mode with valid input."""
        mock_input.return_value = "2"
        
        mode = self.cli.get_response_mode()
        assert mode == 2
    
    @patch('builtins.input')
    @patch('builtins.print')
    def test_get_response_mode_invalid_then_valid(self, mock_print, mock_input):
        """Test getting response mode with invalid then valid input."""
        mock_input.side_effect = ["invalid", "3"]
        
        mode = self.cli.get_response_mode()
        assert mode == 3
        mock_print.assert_called()  # Should print error message
    
    def test_handle_command_exit(self):
        """Test handling exit command."""
        self.cli.conversation_history.current_conversation = []
        
        result = self.cli.handle_command("exit")
        assert result == False  # Should return False to exit
    
    def test_handle_command_exit_with_conversation(self):
        """Test handling exit command with active conversation."""
        self.cli.conversation_history.current_conversation = [{"user": "test", "ai": "response"}]
        
        result = self.cli.handle_command("exit")
        assert result == False
        # Should save conversation before exiting
        self.cli.conversation_history.save_conversation.assert_called_once()
    
    def test_handle_command_clear(self):
        """Test handling clear command."""
        mock_chatbot = Mock()
        self.cli.chatbot = mock_chatbot
        
        result = self.cli.handle_command("clear")
        
        assert result == True
        mock_chatbot.clear_history.assert_called_once()
        self.cli.conversation_history.clear_current_conversation.assert_called_once()
    
    def test_handle_command_mode(self):
        """Test handling mode command."""
        with patch.object(self.cli, 'get_response_mode', return_value=2):
            result = self.cli.handle_command("mode")
        
        assert result == True
        assert self.cli.response_mode == 2
        self.cli.config_manager.set.assert_called_with('default_mode', 2)
    
    def test_handle_command_info(self):
        """Test handling info command."""
        mock_chatbot = Mock()
        mock_chatbot.get_model_info.return_value = {
            'model_name': 'gemini-2.0-flash-exp',
            'generation_config': {'temperature': 0.7, 'max_output_tokens': 8192},
            'has_chat_session': True
        }
        self.cli.chatbot = mock_chatbot
        self.cli.conversation_history.get_conversation_summary.return_value = "Test summary"
        
        with patch('builtins.print') as mock_print:
            result = self.cli.handle_command("info")
        
        assert result == True
        mock_chatbot.get_model_info.assert_called_once()
        mock_print.assert_called()
    
    def test_handle_command_history(self):
        """Test handling history command."""
        with patch.object(self.cli, 'show_conversation_history') as mock_show:
            result = self.cli.handle_command("history")
        
        assert result == True
        mock_show.assert_called_once()
    
    @patch('builtins.input')
    def test_handle_command_save_with_title(self, mock_input):
        """Test handling save command with custom title."""
        mock_input.return_value = "My conversation"
        self.cli.conversation_history.current_conversation = [{"user": "test", "ai": "response"}]
        
        result = self.cli.handle_command("save")
        
        assert result == True
        self.cli.conversation_history.save_conversation.assert_called_with("My conversation")
    
    @patch('builtins.input')
    def test_handle_command_save_empty_conversation(self, mock_input):
        """Test handling save command with empty conversation."""
        self.cli.conversation_history.current_conversation = []
        
        with patch('builtins.print') as mock_print:
            result = self.cli.handle_command("save")
        
        assert result == True
        mock_print.assert_called()  # Should print "No conversation to save"
    
    def test_handle_command_config(self):
        """Test handling config command."""
        with patch.object(self.cli, 'show_configuration') as mock_show:
            result = self.cli.handle_command("config")
        
        assert result == True
        mock_show.assert_called_once()
    
    def test_handle_command_help(self):
        """Test handling help command."""
        with patch.object(self.cli, 'print_welcome') as mock_welcome:
            result = self.cli.handle_command("help")
        
        assert result == True
        mock_welcome.assert_called_once()
    
    def test_handle_command_unknown(self):
        """Test handling unknown command."""
        with patch('builtins.print') as mock_print:
            result = self.cli.handle_command("unknown")
        
        assert result == True
        mock_print.assert_called()  # Should print error message
    
    def test_show_conversation_history_empty(self):
        """Test showing empty conversation history."""
        self.cli.conversation_history.load_conversations.return_value = []
        
        with patch('builtins.print') as mock_print:
            self.cli.show_conversation_history()
        
        mock_print.assert_called()
    
    def test_show_conversation_history_with_data(self):
        """Test showing conversation history with data."""
        mock_conversations = [
            {
                'id': 1,
                'title': 'Test conversation',
                'timestamp': '2024-01-15T10:30:45',
                'exchanges': [{'user': 'Hello', 'ai': 'Hi'}]
            }
        ]
        self.cli.conversation_history.load_conversations.return_value = mock_conversations
        
        with patch('builtins.print') as mock_print:
            self.cli.show_conversation_history()
        
        mock_print.assert_called()
    
    def test_show_configuration(self):
        """Test showing configuration."""
        self.cli.config_manager.config = {
            'default_model': 'gemini-2.0-flash-exp',
            'temperature': 0.7
        }
        
        with patch('builtins.print') as mock_print:
            self.cli.show_configuration()
        
        mock_print.assert_called()
    
    @pytest.mark.asyncio
    async def test_handle_async_response(self):
        """Test handling async response."""
        mock_chatbot = Mock()
        mock_chatbot.respond_async = Mock(return_value="Async response")
        self.cli.chatbot = mock_chatbot
        self.cli.config_manager.get.return_value = True
        
        with patch('builtins.print'):
            await self.cli.handle_async_response("Hello")
        
        mock_chatbot.respond_async.assert_called_once_with("Hello")
        self.cli.conversation_history.add_exchange.assert_called_once_with("Hello", "Async response")
    
    def test_handle_streaming_response(self):
        """Test handling streaming response."""
        mock_chatbot = Mock()
        mock_chatbot.respond_stream.return_value = ["Hello ", "there!"]
        self.cli.chatbot = mock_chatbot
        self.cli.config_manager.get.return_value = True
        
        with patch('builtins.print'):
            self.cli.handle_streaming_response("Hello")
        
        mock_chatbot.respond_stream.assert_called_once_with("Hello")
        self.cli.conversation_history.add_exchange.assert_called_once_with("Hello", "Hello there!")
