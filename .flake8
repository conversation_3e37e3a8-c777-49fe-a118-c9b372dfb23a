[flake8]
max-line-length = 88
extend-ignore = 
    # E203: whitespace before ':' (conflicts with black)
    E203,
    # W503: line break before binary operator (conflicts with black)
    W503,
    # E501: line too long (handled by black)
    E501
exclude = 
    .git,
    __pycache__,
    build,
    dist,
    *.egg-info,
    .venv,
    venv,
    .pytest_cache
per-file-ignores =
    # Allow unused imports in __init__.py files
    __init__.py:F401
    # Allow long lines in test files for readability
    tests/*.py:E501
max-complexity = 10
docstring-convention = google
